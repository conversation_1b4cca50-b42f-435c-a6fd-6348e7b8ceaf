// ملف الوظائف الإضافية للنظام

// إضافة الوظائف للكلاس الرئيسي
Object.assign(BagFactorySystem.prototype, {
    
    // إدارة المستخدمين
    addUser() {
        if (this.currentUser.role !== 'admin') {
            alert('ليس لديك صلاحية لإضافة مستخدمين');
            return;
        }

        const username = document.getElementById('newUsername').value;
        const fullName = document.getElementById('newUserFullName').value;
        const password = document.getElementById('newUserPassword').value;
        const role = document.getElementById('newUserRole').value;

        // جمع الصلاحيات
        const permissions = {
            products: document.getElementById('permProducts').checked,
            inventory: document.getElementById('permInventory').checked,
            workers: document.getElementById('permWorkers').checked,
            attendance: document.getElementById('permAttendance').checked,
            payroll: document.getElementById('permPayroll').checked,
            reports: document.getElementById('permReports').checked
        };

        if (this.users[username]) {
            alert('اسم المستخدم موجود بالفعل');
            return;
        }

        this.users[username] = {
            username,
            name: fullName,
            password,
            role,
            permissions,
            createdAt: new Date().toISOString()
        };

        this.saveUsers();
        this.loadUsersTable();

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
        modal.hide();
        document.getElementById('addUserForm').reset();
        
        alert('تم إضافة المستخدم بنجاح');
    },

    // تحميل جدول المستخدمين
    loadUsersTable() {
        const tbody = document.getElementById('usersTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        Object.values(this.users).forEach(user => {
            if (user.username === 'admin') return; // لا نعرض المدير الرئيسي

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.username}</td>
                <td>
                    <span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-primary'}">
                        ${user.role === 'admin' ? 'مدير' : 'مستخدم'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteUser('${user.username}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // حذف مستخدم
    deleteUser(username) {
        if (this.currentUser.role !== 'admin') {
            alert('ليس لديك صلاحية لحذف المستخدمين');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            delete this.users[username];
            this.saveUsers();
            this.loadUsersTable();
            alert('تم حذف المستخدم بنجاح');
        }
    },

    // تسجيل الحضور للجميع
    markAllPresent() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        this.workers.forEach(worker => {
            if (worker.status === 'active') {
                this.attendance[date][worker.id] = {
                    status: 'present',
                    time: new Date().toLocaleTimeString('ar-EG'),
                    notes: ''
                };
            }
        });

        this.saveAttendance();
        this.loadAttendanceTable();
        alert('تم تسجيل حضور جميع العمال');
    },

    // تحميل جدول الحضور
    loadAttendanceTable() {
        const tbody = document.getElementById('attendanceTable');
        if (!tbody) return;

        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        tbody.innerHTML = '';

        let todayPresent = 0, todayAbsent = 0, todayLate = 0, todayLeft = 0;

        this.workers.forEach(worker => {
            if (worker.status !== 'active') return;

            const attendance = this.attendance[date] && this.attendance[date][worker.id];
            const status = attendance ? attendance.status : 'absent';
            const arrivalTime = attendance ? attendance.arrivalTime : '';
            const leaveTime = attendance ? attendance.leaveTime : '';
            const notes = attendance ? attendance.notes : '';

            // حساب ساعات العمل
            let workingHours = '';
            if (arrivalTime && leaveTime) {
                const arrival = new Date(`${date}T${arrivalTime}`);
                const leave = new Date(`${date}T${leaveTime}`);
                const diffMs = leave - arrival;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                workingHours = `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
            }

            // إحصائيات اليوم
            if (status === 'present') todayPresent++;
            else if (status === 'absent') todayAbsent++;
            else if (status === 'late') todayLate++;
            if (leaveTime) todayLeft++;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${worker.name}</td>
                <td>${worker.job}</td>
                <td>
                    <select class="form-select form-select-sm" onchange="app.updateAttendance('${worker.id}', '${date}', this.value)">
                        <option value="present" ${status === 'present' ? 'selected' : ''}>حاضر</option>
                        <option value="absent" ${status === 'absent' ? 'selected' : ''}>غائب</option>
                        <option value="late" ${status === 'late' ? 'selected' : ''}>متأخر</option>
                    </select>
                </td>
                <td>
                    <input type="time" class="form-control form-control-sm" value="${arrivalTime}"
                           onchange="app.updateArrivalTime('${worker.id}', '${date}', this.value)">
                </td>
                <td>
                    <input type="time" class="form-control form-control-sm" value="${leaveTime}"
                           onchange="app.updateLeaveTime('${worker.id}', '${date}', this.value)">
                </td>
                <td><strong>${workingHours}</strong></td>
                <td>
                    <input type="text" class="form-control form-control-sm" value="${notes}"
                           onchange="app.updateAttendanceNotes('${worker.id}', '${date}', this.value)">
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="app.markPresent('${worker.id}', '${date}')">
                        <i class="bi bi-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="app.markLeave('${worker.id}', '${date}')">
                        <i class="bi bi-door-open"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // تحديث الإحصائيات
        document.getElementById('todayPresent').textContent = todayPresent;
        document.getElementById('todayAbsent').textContent = todayAbsent;
        document.getElementById('todayLate').textContent = todayLate;
        document.getElementById('todayLeft').textContent = todayLeft;
    },

    // تحديث حالة الحضور
    updateAttendance(workerId, date, status) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].status = status;
        this.attendance[date][workerId].time = new Date().toLocaleTimeString('ar-EG');
        
        this.saveAttendance();
    },

    // تحديث ملاحظات الحضور
    updateAttendanceNotes(workerId, date, notes) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].notes = notes;
        this.saveAttendance();
    },

    // تسجيل حضور عامل
    markPresent(workerId, date) {
        this.updateAttendance(workerId, date, 'present');
        this.updateArrivalTime(workerId, date, new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5));
        this.loadAttendanceTable();
    },

    // تسجيل انصراف عامل
    markLeave(workerId, date) {
        this.updateLeaveTime(workerId, date, new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5));
        this.loadAttendanceTable();
    },

    // تسجيل انصراف الكل
    markAllLeave() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5);

        this.workers.forEach(worker => {
            if (worker.status === 'active') {
                this.updateLeaveTime(worker.id, date, currentTime);
            }
        });

        this.loadAttendanceTable();
        alert('تم تسجيل انصراف جميع العمال');
    },

    // تحديث وقت الحضور
    updateArrivalTime(workerId, date, time) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].arrivalTime = time;
        this.saveAttendance();
    },

    // تحديث وقت الانصراف
    updateLeaveTime(workerId, date, time) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].leaveTime = time;
        this.saveAttendance();
    },

    // طباعة حضور اليوم
    printDailyAttendance() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const reportWindow = window.open('', '_blank');

        let presentCount = 0, absentCount = 0, lateCount = 0;

        const attendanceRows = this.workers.filter(w => w.status === 'active').map(worker => {
            const attendance = this.attendance[date] && this.attendance[date][worker.id];
            const status = attendance ? attendance.status : 'absent';
            const arrivalTime = attendance ? attendance.arrivalTime : '';
            const leaveTime = attendance ? attendance.leaveTime : '';
            const notes = attendance ? attendance.notes : '';

            // حساب ساعات العمل
            let workingHours = '';
            if (arrivalTime && leaveTime) {
                const arrival = new Date(`${date}T${arrivalTime}`);
                const leave = new Date(`${date}T${leaveTime}`);
                const diffMs = leave - arrival;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                workingHours = `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
            }

            if (status === 'present') presentCount++;
            else if (status === 'absent') absentCount++;
            else if (status === 'late') lateCount++;

            const statusText = status === 'present' ? 'حاضر' : status === 'absent' ? 'غائب' : 'متأخر';
            const statusClass = status === 'present' ? 'present' : status === 'absent' ? 'absent' : 'late';

            return `
                <tr class="${statusClass}">
                    <td>${worker.name}</td>
                    <td>${worker.job}</td>
                    <td>${statusText}</td>
                    <td>${arrivalTime}</td>
                    <td>${leaveTime}</td>
                    <td>${workingHours}</td>
                    <td>${notes}</td>
                </tr>
            `;
        }).join('');

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حضور يوم ${new Date(date).toLocaleDateString('ar-EG')}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .present { background-color: #e8f5e8; }
                .absent { background-color: #ffebee; }
                .late { background-color: #fff3e0; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير حضور يوم ${new Date(date).toLocaleDateString('ar-EG')}</h1>
                <p>مصنع الشنط - نظام إدارة الحضور والانصراف</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3 style="color: green;">${presentCount}</h3>
                    <p>حاضر</p>
                </div>
                <div class="stat-box">
                    <h3 style="color: red;">${absentCount}</h3>
                    <p>غائب</p>
                </div>
                <div class="stat-box">
                    <h3 style="color: orange;">${lateCount}</h3>
                    <p>متأخر</p>
                </div>
                <div class="stat-box">
                    <h3>${this.workers.filter(w => w.status === 'active').length}</h3>
                    <p>إجمالي العمال</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>الحالة</th>
                        <th>وقت الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>ساعات العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${attendanceRows}
                </tbody>
            </table>

            <div class="footer">
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // طباعة حضور الأسبوع
    printWeeklyAttendance() {
        const selectedDate = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const startDate = new Date(selectedDate);
        const dayOfWeek = startDate.getDay();
        const startOfWeek = new Date(startDate);
        startOfWeek.setDate(startDate.getDate() - dayOfWeek);

        const reportWindow = window.open('', '_blank');

        // إنشاء جدول أسبوعي
        const weekDays = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);
            weekDays.push(date.toISOString().split('T')[0]);
        }

        const weeklyData = this.workers.filter(w => w.status === 'active').map(worker => {
            const weekData = {
                name: worker.name,
                job: worker.job,
                days: {}
            };

            weekDays.forEach(date => {
                const attendance = this.attendance[date] && this.attendance[date][worker.id];
                const status = attendance ? attendance.status : 'absent';
                const arrivalTime = attendance ? attendance.arrivalTime : '';
                const leaveTime = attendance ? attendance.leaveTime : '';

                weekData.days[date] = {
                    status: status,
                    arrivalTime: arrivalTime,
                    leaveTime: leaveTime
                };
            });

            return weekData;
        });

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حضور أسبوعي</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
                .header { text-align: center; margin-bottom: 30px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 4px; text-align: center; font-size: 10px; }
                th { background-color: #f5f5f5; }
                .present { background-color: #e8f5e8; }
                .absent { background-color: #ffebee; }
                .late { background-color: #fff3e0; }
                .worker-name { text-align: right; font-weight: bold; }
                .footer { text-align: center; margin-top: 30px; font-size: 10px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الحضور الأسبوعي</h1>
                <p>من ${new Date(weekDays[0]).toLocaleDateString('ar-EG')} إلى ${new Date(weekDays[6]).toLocaleDateString('ar-EG')}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th rowspan="2">اسم العامل</th>
                        <th rowspan="2">الوظيفة</th>
                        ${weekDays.map(date => `<th colspan="2">${new Date(date).toLocaleDateString('ar-EG', {weekday: 'short', day: 'numeric'})}</th>`).join('')}
                    </tr>
                    <tr>
                        ${weekDays.map(() => '<th>حضور</th><th>انصراف</th>').join('')}
                    </tr>
                </thead>
                <tbody>
                    ${weeklyData.map(worker => `
                        <tr>
                            <td class="worker-name">${worker.name}</td>
                            <td>${worker.job}</td>
                            ${weekDays.map(date => {
                                const dayData = worker.days[date];
                                const statusClass = dayData.status === 'present' ? 'present' : dayData.status === 'absent' ? 'absent' : 'late';
                                return `
                                    <td class="${statusClass}">${dayData.arrivalTime || '-'}</td>
                                    <td class="${statusClass}">${dayData.leaveTime || '-'}</td>
                                `;
                            }).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // حساب المرتبات المحسن
    calculatePayroll() {
        const periodType = document.getElementById('payrollPeriodType').value;
        let startDate, endDate, periodKey;

        // تحديد الفترة حسب النوع
        switch(periodType) {
            case 'daily':
                startDate = endDate = document.getElementById('payrollStartDate').value || new Date().toISOString().split('T')[0];
                periodKey = `daily_${startDate}`;
                break;
            case 'weekly':
                startDate = document.getElementById('payrollStartDate').value;
                if (!startDate) {
                    const today = new Date();
                    const dayOfWeek = today.getDay();
                    const startOfWeek = new Date(today);
                    startOfWeek.setDate(today.getDate() - dayOfWeek);
                    startDate = startOfWeek.toISOString().split('T')[0];
                }
                const weekEnd = new Date(startDate);
                weekEnd.setDate(weekEnd.getDate() + 6);
                endDate = weekEnd.toISOString().split('T')[0];
                periodKey = `weekly_${startDate}`;
                break;
            case 'monthly':
                const month = document.getElementById('payrollMonth').value;
                if (!month) {
                    alert('يرجى اختيار الشهر');
                    return;
                }
                const [year, monthNum] = month.split('-');
                startDate = `${year}-${monthNum}-01`;
                endDate = new Date(year, monthNum, 0).toISOString().split('T')[0];
                periodKey = month;
                break;
            case 'yearly':
                const selectedYear = document.getElementById('payrollYear').value;
                if (!selectedYear) {
                    alert('يرجى اختيار السنة');
                    return;
                }
                startDate = `${selectedYear}-01-01`;
                endDate = `${selectedYear}-12-31`;
                periodKey = `yearly_${selectedYear}`;
                break;
        }

        if (!startDate || !endDate) {
            alert('يرجى تحديد الفترة الزمنية');
            return;
        }

        // حساب عدد الأيام في الفترة
        const start = new Date(startDate);
        const end = new Date(endDate);
        const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

        let totalPayroll = 0;
        let totalWorkingDays = 0;
        let totalAbsentDays = 0;
        let totalHours = 0;
        let totalBonuses = 0;
        let totalDeductions = 0;

        this.workers.forEach(worker => {
            if (worker.status !== 'active') return;

            let presentDays = 0;
            let absentDays = 0;
            let totalWorkerHours = 0;

            // حساب أيام الحضور والغياب والساعات
            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                const dateStr = d.toISOString().split('T')[0];
                const attendance = this.attendance[dateStr] && this.attendance[dateStr][worker.id];

                if (attendance && (attendance.status === 'present' || attendance.status === 'late')) {
                    presentDays++;

                    // حساب ساعات العمل
                    if (attendance.arrivalTime && attendance.leaveTime) {
                        const arrival = new Date(`${dateStr}T${attendance.arrivalTime}`);
                        const leave = new Date(`${dateStr}T${attendance.leaveTime}`);
                        const diffMs = leave - arrival;
                        const diffHours = diffMs / (1000 * 60 * 60);
                        totalWorkerHours += diffHours;
                    }
                } else {
                    absentDays++;
                }
            }

            const basicSalary = presentDays * worker.dailySalary;
            const bonuses = 0; // سيتم تحديثها لاحقاً
            const deductions = 0; // سيتم تحديثها لاحقاً
            const netSalary = basicSalary + bonuses - deductions;

            totalPayroll += netSalary;
            totalWorkingDays += presentDays;
            totalAbsentDays += absentDays;
            totalHours += totalWorkerHours;
            totalBonuses += bonuses;
            totalDeductions += deductions;

            // حفظ بيانات المرتب
            if (!this.payroll[periodKey]) {
                this.payroll[periodKey] = {};
            }

            this.payroll[periodKey][worker.id] = {
                workerId: worker.id,
                workerName: worker.name,
                dailySalary: worker.dailySalary,
                presentDays,
                absentDays,
                totalHours: Math.round(totalWorkerHours * 100) / 100,
                bonuses,
                deductions,
                netSalary: basicSalary,
                finalSalary: netSalary,
                periodType,
                startDate,
                endDate,
                calculatedAt: new Date().toISOString()
            };
        });

        // تحديث الإحصائيات
        document.getElementById('totalPayroll').textContent = totalPayroll.toLocaleString() + ' ج.م';
        document.getElementById('workingDays').textContent = totalWorkingDays;
        document.getElementById('absentDays').textContent = totalAbsentDays;
        document.getElementById('totalHours').textContent = Math.round(totalHours);
        document.getElementById('totalBonuses').textContent = totalBonuses.toLocaleString() + ' ج.م';
        document.getElementById('totalDeductions').textContent = totalDeductions.toLocaleString() + ' ج.م';

        // تحديث معلومات الفترة
        document.getElementById('payrollPeriodInfo').innerHTML = `
            <small><strong>${this.getPeriodTypeText(periodType)}</strong></small>
        `;
        document.getElementById('periodStartInfo').textContent = new Date(startDate).toLocaleDateString('ar-EG');
        document.getElementById('periodEndInfo').textContent = new Date(endDate).toLocaleDateString('ar-EG');
        document.getElementById('periodDaysInfo').textContent = `عدد الأيام: ${totalDays}`;

        this.savePayroll();
        this.loadPayrollTable(periodKey);
        alert(`تم حساب المرتبات ${this.getPeriodTypeText(periodType)} بنجاح`);
    },

    // الحصول على نص نوع الفترة
    getPeriodTypeText(periodType) {
        const types = {
            'daily': 'اليومية',
            'weekly': 'الأسبوعية',
            'monthly': 'الشهرية',
            'yearly': 'السنوية'
        };
        return types[periodType] || 'غير محدد';
    },

    // تحميل جدول المرتبات
    loadPayrollTable(month) {
        const tbody = document.getElementById('payrollTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (!month || !this.payroll[month]) return;

        Object.values(this.payroll[month]).forEach(payroll => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${payroll.workerName}</td>
                <td>${payroll.dailySalary} ج.م</td>
                <td>${payroll.presentDays}</td>
                <td>${payroll.absentDays}</td>
                <td>
                    <input type="number" class="form-control form-control-sm" value="${payroll.bonuses}" 
                           onchange="app.updatePayrollBonuses('${month}', '${payroll.workerId}', this.value)">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" value="${payroll.deductions}" 
                           onchange="app.updatePayrollDeductions('${month}', '${payroll.workerId}', this.value)">
                </td>
                <td><strong>${(payroll.netSalary + payroll.bonuses - payroll.deductions).toLocaleString()} ج.م</strong></td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="app.printPayslip('${month}', '${payroll.workerId}')">
                        <i class="bi bi-printer"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحديث الحوافز
    updatePayrollBonuses(month, workerId, bonuses) {
        if (this.payroll[month] && this.payroll[month][workerId]) {
            this.payroll[month][workerId].bonuses = parseFloat(bonuses) || 0;
            this.savePayroll();
            this.loadPayrollTable(month);
        }
    },

    // تحديث الخصومات
    updatePayrollDeductions(month, workerId, deductions) {
        if (this.payroll[month] && this.payroll[month][workerId]) {
            this.payroll[month][workerId].deductions = parseFloat(deductions) || 0;
            this.savePayroll();
            this.loadPayrollTable(month);
        }
    },

    // طباعة كشف مرتب
    printPayslip(month, workerId) {
        const payroll = this.payroll[month] && this.payroll[month][workerId];
        if (!payroll) {
            alert('لا توجد بيانات مرتب لهذا العامل في الشهر المحدد');
            return;
        }

        const netSalary = payroll.netSalary + payroll.bonuses - payroll.deductions;
        const reportWindow = window.open('', '_blank');

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>كشف مرتب - ${payroll.workerName}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .company-info { text-align: center; margin-bottom: 20px; }
                .payslip-info { margin-bottom: 30px; }
                .info-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 5px 0; }
                .info-row:nth-child(even) { background-color: #f9f9f9; }
                .total-row { border-top: 2px solid #333; font-weight: bold; font-size: 1.2em; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="company-info">
                <h2>مصنع الشنط</h2>
                <p>كشف مرتب شهري</p>
            </div>

            <div class="header">
                <h1>كشف مرتب - ${payroll.workerName}</h1>
                <p>الشهر: ${new Date(month + '-01').toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' })}</p>
            </div>

            <div class="payslip-info">
                <div class="info-row">
                    <span>اسم العامل:</span>
                    <span><strong>${payroll.workerName}</strong></span>
                </div>
                <div class="info-row">
                    <span>الراتب اليومي:</span>
                    <span>${payroll.dailySalary} ج.م</span>
                </div>
                <div class="info-row">
                    <span>أيام الحضور:</span>
                    <span>${payroll.presentDays} يوم</span>
                </div>
                <div class="info-row">
                    <span>أيام الغياب:</span>
                    <span>${payroll.absentDays} يوم</span>
                </div>
                <div class="info-row">
                    <span>الراتب الأساسي:</span>
                    <span>${payroll.netSalary.toLocaleString()} ج.م</span>
                </div>
                <div class="info-row">
                    <span>الحوافز:</span>
                    <span>${payroll.bonuses} ج.م</span>
                </div>
                <div class="info-row">
                    <span>الخصومات:</span>
                    <span>${payroll.deductions} ج.م</span>
                </div>
                <div class="info-row total-row">
                    <span>صافي المرتب:</span>
                    <span><strong>${netSalary.toLocaleString()} ج.م</strong></span>
                </div>
            </div>

            <div style="margin-top: 50px; text-align: center;">
                <p>التاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
                <br><br>
                <div style="display: flex; justify-content: space-between;">
                    <div>
                        <p>توقيع العامل</p>
                        <p>_________________</p>
                    </div>
                    <div>
                        <p>توقيع المحاسب</p>
                        <p>_________________</p>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    },

    // تحديث إحصائيات المخزون
    updateInventoryStats() {
        const totalStockValue = this.products.reduce((sum, product) =>
            sum + (product.price * product.quantity), 0);
        const lowStockItems = this.products.filter(product => product.quantity < 10).length;
        const todayProduction = Math.floor(Math.random() * 100) + 50; // بيانات وهمية

        document.getElementById('totalStockValue').textContent = totalStockValue.toLocaleString() + ' ج.م';
        document.getElementById('lowStockItems').textContent = lowStockItems;
        document.getElementById('todayProduction').textContent = todayProduction;
    },

    // تحميل جدول المخزون
    loadInventoryTable() {
        const tbody = document.getElementById('inventoryTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.products.forEach(product => {
            const lastProduction = new Date().toLocaleDateString('ar-EG');
            const status = product.quantity === 0 ? 'نفد المخزون' :
                          product.quantity < 10 ? 'مخزون منخفض' : 'متوفر';
            const statusClass = product.quantity === 0 ? 'danger' :
                               product.quantity < 10 ? 'warning' : 'success';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${product.quantity}</td>
                <td>10</td>
                <td>${lastProduction}</td>
                <td>
                    <span class="badge bg-${statusClass}">${status}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="app.addStock('${product.id}')">
                        <i class="bi bi-plus-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.removeStock('${product.id}')">
                        <i class="bi bi-dash-circle"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إضافة كمية للمخزون
    addStock(productId) {
        const quantity = prompt('كم قطعة تريد إضافتها؟');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            const product = this.products.find(p => p.id === productId);
            if (product) {
                const previousQuantity = product.quantity;
                product.quantity += parseInt(quantity);
                product.totalPrice = product.price * product.quantity; // تحديث السعر الإجمالي

                // تسجيل الحركة في النظام المفصل
                this.recordInventoryMovement(
                    product,
                    'add',
                    parseInt(quantity),
                    previousQuantity,
                    'إضافة سريعة للمخزون',
                    'quick_add'
                );

                // حفظ العملية في سجل المخزون القديم للتوافق
                const today = new Date().toISOString().split('T')[0];
                if (!this.inventory[today]) {
                    this.inventory[today] = [];
                }

                this.inventory[today].push({
                    id: this.generateId(),
                    productId: product.id,
                    productName: product.name,
                    productCode: product.code,
                    operation: 'add',
                    quantity: parseInt(quantity),
                    notes: 'إضافة سريعة للمخزون',
                    date: today,
                    timestamp: new Date().toISOString(),
                    user: this.currentUser.name
                });

                this.saveInventory();
                this.saveProducts();
                this.loadInventoryTable();
                this.loadProductsTable();
                this.loadInventoryMovementTable(); // تحديث الجدول الجديد
                this.updateInventoryStats();
                this.updateDashboard();
                alert(`تم إضافة ${quantity} قطعة للمخزون`);
            }
        }
    },

    // خصم كمية من المخزون
    removeStock(productId) {
        const quantity = prompt('كم قطعة تريد خصمها؟');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            const product = this.products.find(p => p.id === productId);
            if (product) {
                if (product.quantity >= parseInt(quantity)) {
                    const previousQuantity = product.quantity;
                    product.quantity -= parseInt(quantity);
                    product.totalPrice = product.price * product.quantity; // تحديث السعر الإجمالي

                    // تسجيل الحركة في النظام المفصل
                    this.recordInventoryMovement(
                        product,
                        'remove',
                        parseInt(quantity),
                        previousQuantity,
                        'خصم سريع من المخزون',
                        'quick_remove'
                    );

                    // حفظ العملية في سجل المخزون القديم للتوافق
                    const today = new Date().toISOString().split('T')[0];
                    if (!this.inventory[today]) {
                        this.inventory[today] = [];
                    }

                    this.inventory[today].push({
                        id: this.generateId(),
                        productId: product.id,
                        productName: product.name,
                        productCode: product.code,
                        operation: 'remove',
                        quantity: parseInt(quantity),
                        notes: 'خصم سريع من المخزون',
                        date: today,
                        timestamp: new Date().toISOString(),
                        user: this.currentUser.name
                    });

                    this.saveInventory();
                    this.saveProducts();
                    this.loadInventoryTable();
                    this.loadProductsTable();
                    this.loadInventoryMovementTable(); // تحديث الجدول الجديد
                    this.updateInventoryStats();
                    this.updateDashboard();
                    alert(`تم خصم ${quantity} قطعة من المخزون`);
                } else {
                    alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
                }
            }
        }
    },

    // تحديث إعدادات المستخدم
    updateUserSettings() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        if (this.currentUser.password !== currentPassword) {
            alert('كلمة المرور الحالية غير صحيحة');
            return;
        }

        if (newPassword !== confirmPassword) {
            alert('كلمة المرور الجديدة غير متطابقة');
            return;
        }

        if (newPassword.length < 6) {
            alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // تحديث كلمة المرور
        this.users[this.currentUser.username].password = newPassword;
        this.currentUser.password = newPassword;

        this.saveUsers();
        localStorage.setItem('bagFactory_currentUser', JSON.stringify(this.currentUser));

        // إعادة تعيين النموذج
        document.getElementById('userSettingsForm').reset();

        alert('تم تحديث كلمة المرور بنجاح');
    },

    // تصدير البيانات
    exportData() {
        const data = {
            users: this.users,
            products: this.products,
            workers: this.workers,
            attendance: this.attendance,
            inventory: this.inventory,
            payroll: this.payroll,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bag_factory_backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        alert('تم تصدير البيانات بنجاح');
    },

    // معالجة عمليات المخزون
    processStockOperation() {
        const productId = document.getElementById('stockProductSelect').value;
        const quantity = parseInt(document.getElementById('stockQuantity').value);
        const operation = document.getElementById('stockOperation').value;
        const notes = document.getElementById('stockNotes').value;

        if (!productId || !quantity || quantity <= 0) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const product = this.products.find(p => p.id === productId);
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const previousQuantity = product.quantity;

        if (operation === 'add') {
            product.quantity += quantity;
            alert(`تم إضافة ${quantity} قطعة إلى ${product.name}`);
        } else if (operation === 'remove') {
            if (product.quantity < quantity) {
                alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
                return;
            }
            product.quantity -= quantity;
            alert(`تم خصم ${quantity} قطعة من ${product.name}`);
        }

        // تحديث السعر الإجمالي
        product.totalPrice = product.price * product.quantity;

        // تسجيل الحركة في النظام المفصل
        this.recordInventoryMovement(
            product,
            operation,
            quantity,
            previousQuantity,
            notes,
            'manual'
        );

        // حفظ العملية في سجل المخزون القديم للتوافق
        const today = new Date().toISOString().split('T')[0];
        if (!this.inventory[today]) {
            this.inventory[today] = [];
        }

        this.inventory[today].push({
            id: this.generateId(),
            productId: product.id,
            productName: product.name,
            productCode: product.code,
            operation: operation,
            quantity: quantity,
            notes: notes,
            date: today,
            timestamp: new Date().toISOString(),
            user: this.currentUser.name
        });

        // حفظ البيانات
        this.saveProducts();
        this.saveInventory();

        // تحديث الواجهة
        this.updateDashboard();
        this.loadProductsTable();
        this.loadInventoryTable();
        this.loadInventoryMovementTable(); // تحديث الجدول الجديد
        this.updateInventoryStats();

        // إغلاق النافذة وإعادة تعيين النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addStockModal'));
        modal.hide();
        document.getElementById('addStockForm').reset();
    },

    // استيراد البيانات
    importData() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) {
            alert('يرجى اختيار ملف للاستيراد');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                    this.users = data.users || this.users;
                    this.products = data.products || [];
                    this.workers = data.workers || [];
                    this.attendance = data.attendance || {};
                    this.inventory = data.inventory || {};
                    this.payroll = data.payroll || {};

                    // حفظ البيانات
                    this.saveUsers();
                    this.saveProducts();
                    this.saveWorkers();
                    this.saveAttendance();
                    this.saveInventory();
                    this.savePayroll();

                    // تحديث الواجهة
                    this.updateDashboard();
                    this.loadProductsTable();
                    this.loadWorkersTable();

                    alert('تم استيراد البيانات بنجاح');
                }
            } catch (error) {
                alert('خطأ في قراءة الملف: ' + error.message);
            }
        };

        reader.readAsText(file);
    },

    // تعديل عامل
    editWorker(workerId) {
        const worker = this.workers.find(w => w.id === workerId);
        if (!worker) return;

        // ملء النموذج ببيانات العامل
        document.getElementById('workerName').value = worker.name;
        document.getElementById('workerJob').value = worker.job;
        document.getElementById('workerSalary').value = worker.dailySalary;
        document.getElementById('workerPhone').value = worker.phone || '';
        document.getElementById('workerAddress').value = worker.address || '';
        document.getElementById('workerHireDate').value = worker.hireDate;

        // تغيير عنوان النافذة والزر
        document.querySelector('#addWorkerModal .modal-title').innerHTML =
            '<i class="bi bi-pencil me-2"></i>تعديل العامل';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>حفظ التعديلات';

        // تغيير وظيفة الزر
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').onclick = () => {
            this.updateWorker(workerId);
        };

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('addWorkerModal'));
        modal.show();
    },

    // تحديث عامل
    updateWorker(workerId) {
        const workerIndex = this.workers.findIndex(w => w.id === workerId);
        if (workerIndex === -1) return;

        const updatedWorker = {
            ...this.workers[workerIndex],
            name: document.getElementById('workerName').value,
            job: document.getElementById('workerJob').value,
            dailySalary: parseFloat(document.getElementById('workerSalary').value),
            phone: document.getElementById('workerPhone').value,
            address: document.getElementById('workerAddress').value,
            hireDate: document.getElementById('workerHireDate').value,
            updatedAt: new Date().toISOString()
        };

        this.workers[workerIndex] = updatedWorker;
        this.saveWorkers();
        this.loadWorkersTable();
        this.updateDashboard();

        // إغلاق النافذة وإعادة تعيين النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addWorkerModal'));
        modal.hide();
        this.resetWorkerModal();

        alert('تم تحديث العامل بنجاح');
    },

    // حذف عامل
    deleteWorker(workerId) {
        if (confirm('هل أنت متأكد من حذف هذا العامل؟')) {
            this.workers = this.workers.filter(w => w.id !== workerId);
            this.saveWorkers();
            this.loadWorkersTable();
            this.updateDashboard();
            alert('تم حذف العامل بنجاح');
        }
    },

    // إعادة تعيين نافذة العامل
    resetWorkerModal() {
        document.querySelector('#addWorkerModal .modal-title').innerHTML =
            '<i class="bi bi-person-plus me-2"></i>إضافة عامل جديد';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>إضافة العامل';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').onclick = () => {
            this.addWorker();
        };
        document.getElementById('addWorkerForm').reset();
    },

    // إضافة الشهر القادم
    addNextMonth() {
        const monthSelect = document.getElementById('payrollMonth');
        const currentDate = new Date();
        const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        const nextMonthValue = nextMonth.toISOString().slice(0, 7);
        const nextMonthText = nextMonth.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' });

        // التحقق من عدم وجود الشهر مسبقاً
        const existingOption = Array.from(monthSelect.options).find(option => option.value === nextMonthValue);
        if (existingOption) {
            alert('الشهر القادم موجود بالفعل في القائمة');
            monthSelect.value = nextMonthValue;
            return;
        }

        // إضافة الشهر الجديد
        const option = document.createElement('option');
        option.value = nextMonthValue;
        option.textContent = nextMonthText;
        monthSelect.appendChild(option);
        monthSelect.value = nextMonthValue;

        alert(`تم إضافة ${nextMonthText} إلى قائمة الشهور`);
    },

    // إضافة السنة القادمة
    addNextYear() {
        const yearSelect = document.getElementById('payrollYear');
        const currentYear = new Date().getFullYear();
        const nextYear = currentYear + 1;

        // التحقق من عدم وجود السنة مسبقاً
        const existingOption = Array.from(yearSelect.options).find(option => option.value === nextYear.toString());
        if (existingOption) {
            alert('السنة القادمة موجودة بالفعل في القائمة');
            yearSelect.value = nextYear.toString();
            return;
        }

        // إضافة السنة الجديدة
        const option = document.createElement('option');
        option.value = nextYear.toString();
        option.textContent = nextYear.toString();
        yearSelect.appendChild(option);
        yearSelect.value = nextYear.toString();

        alert(`تم إضافة سنة ${nextYear} إلى قائمة السنوات`);
    },

    // تصدير البيانات
    exportData() {
        const data = {
            users: this.users,
            products: this.products,
            workers: this.workers,
            attendance: this.attendance,
            inventory: this.inventory,
            payroll: this.payroll,
            materials: this.materials,
            categories: this.categories,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bag_factory_backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        alert('تم تصدير البيانات بنجاح');
    },

    // استيراد البيانات
    importData() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) {
            alert('يرجى اختيار ملف للاستيراد');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                    this.users = data.users || this.users;
                    this.products = data.products || [];
                    this.workers = data.workers || [];
                    this.attendance = data.attendance || {};
                    this.inventory = data.inventory || {};
                    this.payroll = data.payroll || {};
                    this.materials = data.materials || [];
                    this.categories = data.categories || [];

                    // حفظ البيانات
                    this.saveUsers();
                    this.saveProducts();
                    this.saveWorkers();
                    this.saveAttendance();
                    this.saveInventory();
                    this.savePayroll();
                    this.saveMaterials();
                    this.saveCategories();

                    // تحديث الواجهة
                    this.updateDashboard();
                    this.loadProductsTable();
                    this.loadWorkersTable();
                    this.loadMaterialsTable();
                    this.loadCategoriesTable();

                    alert('تم استيراد البيانات بنجاح');
                }
            } catch (error) {
                alert('خطأ في قراءة الملف: ' + error.message);
            }
        };

        reader.readAsText(file);
    },

    // تحديث إعدادات المستخدم
    updateUserSettings() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        if (this.currentUser.password !== currentPassword) {
            alert('كلمة المرور الحالية غير صحيحة');
            return;
        }

        if (newPassword !== confirmPassword) {
            alert('كلمة المرور الجديدة غير متطابقة');
            return;
        }

        if (newPassword.length < 6) {
            alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // تحديث كلمة المرور
        this.users[this.currentUser.username].password = newPassword;
        this.currentUser.password = newPassword;

        this.saveUsers();
        localStorage.setItem('bagFactory_currentUser', JSON.stringify(this.currentUser));

        // إعادة تعيين النموذج
        document.getElementById('userSettingsForm').reset();

        alert('تم تحديث كلمة المرور بنجاح');
    }
});

// تهيئة قائمة الشهور والسنوات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // ملء قائمة الشهور
    const payrollMonth = document.getElementById('payrollMonth');
    if (payrollMonth) {
        const currentDate = new Date();
        for (let i = 0; i < 24; i++) { // زيادة إلى 24 شهر (سنتين)
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const value = date.toISOString().slice(0, 7);
            const text = date.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' });

            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            payrollMonth.appendChild(option);
        }
    }

    // ملء قائمة السنوات
    const payrollYear = document.getElementById('payrollYear');
    if (payrollYear) {
        const currentYear = new Date().getFullYear();
        for (let i = -2; i <= 2; i++) { // من سنتين سابقتين إلى سنتين قادمتين
            const year = currentYear + i;
            const option = document.createElement('option');
            option.value = year.toString();
            option.textContent = year.toString();
            payrollYear.appendChild(option);
        }
        payrollYear.value = currentYear.toString(); // تحديد السنة الحالية
    }

    // تعيين التاريخ الحالي لحقل الحضور
    const attendanceDate = document.getElementById('attendanceDate');
    if (attendanceDate) {
        attendanceDate.value = new Date().toISOString().split('T')[0];
    }

    // تعيين التواريخ الافتراضية للمرتبات
    const payrollStartDate = document.getElementById('payrollStartDate');
    const payrollEndDate = document.getElementById('payrollEndDate');
    if (payrollStartDate && payrollEndDate) {
        const today = new Date();
        payrollStartDate.value = today.toISOString().split('T')[0];
        payrollEndDate.value = today.toISOString().split('T')[0];
    }

    // إضافة مستمع لتغيير نوع الفترة
    const payrollPeriodType = document.getElementById('payrollPeriodType');
    if (payrollPeriodType) {
        payrollPeriodType.addEventListener('change', function() {
            updatePayrollPeriodFields(this.value);
        });
        // تحديث الحقول عند التحميل
        updatePayrollPeriodFields(payrollPeriodType.value);
    }
});

// تحديث حقول الفترة حسب النوع المختار
function updatePayrollPeriodFields(periodType) {
    const startDateField = document.getElementById('payrollStartDate');
    const endDateField = document.getElementById('payrollEndDate');
    const monthField = document.getElementById('payrollMonth');
    const yearField = document.getElementById('payrollYear');

    // إخفاء جميع الحقول أولاً
    [startDateField, endDateField, monthField, yearField].forEach(field => {
        if (field) field.style.display = 'none';
    });

    // إظهار الحقول المناسبة حسب نوع الفترة
    switch(periodType) {
        case 'daily':
            if (startDateField) startDateField.style.display = 'inline-block';
            break;
        case 'weekly':
            if (startDateField) startDateField.style.display = 'inline-block';
            break;
        case 'monthly':
            if (monthField) monthField.style.display = 'inline-block';
            break;
        case 'yearly':
            if (yearField) yearField.style.display = 'inline-block';
            break;
    }
}
